import os
import asyncmy

class DatabaseService:
    _instance = None
    _pool = None

    @classmethod
    async def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
            await cls._instance._initialize_pool()
        return cls._instance

    async def _initialize_pool(self):
        # 从环境变量获取数据库配置
        try:
            self._pool = await asyncmy.create_pool(
                host=os.getenv('MYSQL_HOST', 'localhost'),
                port=int(os.getenv('MYSQL_PORT', '3306')),
                user=os.getenv('MYSQL_USER', 'root'),
                password=os.getenv('MYSQL_PASSWORD', '123456'),
                db=os.getenv('MYSQL_DATABASE', 'testdb'),
                autocommit=True
            )
            # 确保连接池已完全初始化
            async with self._pool.acquire() as conn:
                await conn.ping()
        except Exception as e:
            self._pool = None
            raise RuntimeError(f"Failed to initialize database pool: {e}")

    async def execute_query(self, query, params=None):
        #输出完整sql语句,query为模版，params为参数
        print(f"Executing SQL query: {query}, Params: {params}")
        if self._pool is None:
            await self._initialize_pool()

        try:
            async with self._pool.acquire() as conn:
                async with conn.cursor(asyncmy.cursors.DictCursor) as cur:
                    await cur.execute(query, params)
                    result = await cur.fetchall()
                    return result
        except RuntimeError as e:
            if "Event loop is closed" in str(e):
                # 事件循环已关闭，重新初始化连接池
                await self._initialize_pool()
                async with self._pool.acquire() as conn:
                    async with conn.cursor(asyncmy.cursors.DictCursor) as cur:
                        await cur.execute(query, params)
                        result = await cur.fetchall()
                        return result
            raise

    @classmethod
    async def close_pool(cls):
        if cls._instance and cls._instance._pool:
            cls._instance._pool.close()
            await cls._instance._pool.wait_closed()
            cls._instance._pool = None
            cls._instance = None

# 确保在应用启动时初始化数据库连接
async def initialize_database():
    try:
        db_service = await DatabaseService.get_instance()
        print("Database connection initialized successfully")
    except Exception as e:
        print(f"Failed to initialize database connection: {e}")