version: 1

apps:
  - appID: recommend
    appDirPath: ../
    appPort: 9050
    command: ["uv", "run", "main.py"]
    env:
      SERVER_HOST: "0.0.0.0"
      SERVER_PORT: "9050"

      MYSQL_HOST: "*********"
      MYSQL_PORT: "3306"
      MYSQL_USER: "crawler_user"
      MYSQL_PASSWORD: "jAhp6qFWXj"
      MYSQL_DATABASE: "crawler_db"

      LOG_LEVEL: "debug"
      
    # Dapr sidecar配置
    daprHTTPPort: 3570
    daprGRPCPort: 35070
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6160
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3